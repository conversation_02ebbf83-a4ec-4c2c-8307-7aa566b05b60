package bot

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func ChatBotSuggest(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type SupportReq struct {
			ChatID   string `json:"chat_id" validate:"required"`
			Workflow string `json:"workflow"`
		}
		supReq := new(SupportReq)

		err := c.Bind(supReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(supReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.Prepare("SELECT first_name FROM chatbot WHERE id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r := stmt.QueryRowContext(c.Request().Context(), supReq.ChatID)
		var username string
		if err = r.Scan(&username); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"vars": []map[string]interface{}{
				{
					"message": fmt.Sprintf("Chào %s, mình có thể giúp gì được cho bạn?", username),
				},
			},
		})
	}
}

func SendChatTemplate(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type templateReq struct {
			ChatID   string        `json:"chat_id" validate:"required"`
			Template string        `json:"template" validate:"required"`
			List     []interface{} `json:"list"`
		}
		templReq := new(templateReq)

		err := c.Bind(templReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(templReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		for _, item := range templReq.List {
			b := new(bytes.Buffer)
			err = s.Template.Render(b, templReq.Template, item, c)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			paramChannel := map[string]string{
				"chat_id": templReq.ChatID,
				"message": b.String(),
			}
			paramChannelData, err := json.Marshal(paramChannel)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			job := &model.NanoJob{
				Url:    fmt.Sprintf("http://localhost:%s/v1/channel/ask", s.Config.HTTPPort),
				Params: string(paramChannelData),
				Method: http.MethodPost,
				Owner:  templReq.ChatID,
			}
			err = s.Pacman.IngestJob(c.Request().Context(), job)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}
