package middleware

import (
	"context"
	"database/sql"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/service"

	"github.com/labstack/echo/v4"
)

func TransactionHandler(db *sql.DB, s *service.Service) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			req := c.Request()
			if req.Method != "GET" {
				tx, err := db.BeginTx(req.Context(), &sql.TxOptions{Isolation: sql.LevelRepeatableRead, ReadOnly: false})
				if err != nil {
					return err
				}
				c.SetRequest(req.WithContext(context.WithValue(req.Context(), constants.ContextTransactionKey, tx)))
				if err := next(c); err != nil {
					err = tx.Rollback()
					if err != nil {
						c.<PERSON>gger().Debug("Transaction Rollback: ", err)
					}
					return err
				}
				err = tx.Commit()
				if err != nil {
					c.Logger().Debug("Transaction Commit: ", err)
				} else {
					// Nudge the queue after successful commit
					s.Pacman.NudgeQueue()
				}
			} else {
				c.SetRequest(req.WithContext(context.WithValue(req.Context(), constants.ContextTransactionKey, db)))
				return next(c)
			}
			return nil
		}
	}
}
